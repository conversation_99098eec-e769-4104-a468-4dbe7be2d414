// Register GSAP plugins
gsap.registerPlugin(ScrollTrigger);

// DOM Elements
const hamburger = document.querySelector('.hamburger');
const navMenu = document.querySelector('.nav-menu');
const modal = document.getElementById('pdfModal');
const closeModal = document.querySelector('.close');
const downloadBtns = document.querySelectorAll('.download-btn');
const playButtons = document.querySelectorAll('.play-button');
const trapItems = document.querySelectorAll('.trap-item');
const phaseCards = document.querySelectorAll('.phase-card');
const prevPhaseBtn = document.getElementById('prevPhase');
const nextPhaseBtn = document.getElementById('nextPhase');
const mirrorBtn = document.getElementById('mirrorBtn');
const nextQuestionBtn = document.getElementById('nextQuestion');
const questions = document.querySelectorAll('.question');
const finalQuote = document.getElementById('finalQuote');
const brotherhoodForm = document.getElementById('brotherhoodForm');
const startJourneyBtn = document.getElementById('startJourney');
const progressBar = document.getElementById('progressBar');
const indicators = document.querySelectorAll('.indicator');
const sections = document.querySelectorAll('section');

// State
let currentPhase = 0;
let currentQuestion = 0;
let isPlaying = {};

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    initAnimations();
    initEventListeners();
    initScrollAnimations();
    initParallax();
    initProgressTracking();
    initSectionIndicators();
});

// Navigation Toggle
function initEventListeners() {
    // Mobile menu toggle
    hamburger.addEventListener('click', () => {
        hamburger.classList.toggle('active');
        navMenu.classList.toggle('active');
    });

    // Close mobile menu when clicking on links
    document.querySelectorAll('.nav-menu a').forEach(link => {
        link.addEventListener('click', () => {
            hamburger.classList.remove('active');
            navMenu.classList.remove('active');
        });
    });

    // Modal functionality
    downloadBtns.forEach(btn => {
        btn.addEventListener('click', openModal);
    });

    closeModal.addEventListener('click', closeModalFunc);
    window.addEventListener('click', (e) => {
        if (e.target === modal) closeModalFunc();
    });

    // Audio/Video therapy cards
    playButtons.forEach(button => {
        button.addEventListener('click', toggleAudio);
    });

    // Trap items animation
    trapItems.forEach(item => {
        item.addEventListener('click', animateTrap);
    });

    // Phase slider
    prevPhaseBtn.addEventListener('click', () => changePhase(-1));
    nextPhaseBtn.addEventListener('click', () => changePhase(1));

    // Mirror section
    nextQuestionBtn.addEventListener('click', nextQuestion);

    // Brotherhood form
    brotherhoodForm.addEventListener('submit', handleBrotherhoodSubmit);

    // Start journey button
    startJourneyBtn.addEventListener('click', () => {
        gsap.to(window, {duration: 2, scrollTo: "#truth"});
    });

    // Smooth scrolling for navigation links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                gsap.to(window, {duration: 1.5, scrollTo: target});
            }
        });
    });

    // Section indicators click handlers
    indicators.forEach(indicator => {
        indicator.addEventListener('click', () => {
            const sectionId = indicator.getAttribute('data-section');
            const target = document.getElementById(sectionId);
            if (target) {
                gsap.to(window, {duration: 1.5, scrollTo: target});
            }
        });
    });
}

// Initial Animations
function initAnimations() {
    // Hero section entrance
    const tl = gsap.timeline();
    
    tl.from('.hero-title', {
        duration: 1.5,
        y: 100,
        opacity: 0,
        ease: "power3.out"
    })
    .from('.hero-subtitle', {
        duration: 1,
        y: 50,
        opacity: 0,
        ease: "power2.out"
    }, "-=0.5")
    .from('.cta-button', {
        duration: 1,
        scale: 0,
        opacity: 0,
        ease: "back.out(1.7)"
    }, "-=0.3")
    .from('.scroll-indicator', {
        duration: 0.8,
        y: 30,
        opacity: 0,
        ease: "power2.out"
    }, "-=0.2");

    // Glitch text effect
    const glitchTexts = document.querySelectorAll('.glitch-text');
    glitchTexts.forEach(text => {
        text.setAttribute('data-text', text.textContent);
    });
}

// Scroll Animations
function initScrollAnimations() {
    // Section titles
    gsap.utils.toArray('.section-title').forEach(title => {
        gsap.from(title, {
            scrollTrigger: {
                trigger: title,
                start: "top 80%",
                end: "bottom 20%",
                toggleActions: "play none none reverse"
            },
            duration: 1,
            y: 50,
            opacity: 0,
            ease: "power2.out"
        });
    });

    // Truth section animations
    gsap.from('.trap-item', {
        scrollTrigger: {
            trigger: '.truth-section',
            start: "top 70%"
        },
        duration: 0.8,
        y: 50,
        opacity: 0,
        stagger: 0.2,
        ease: "power2.out"
    });

    gsap.from('.destruction-animation', {
        scrollTrigger: {
            trigger: '.destruction-animation',
            start: "top 80%"
        },
        duration: 1,
        scale: 0.8,
        opacity: 0,
        ease: "back.out(1.7)"
    });

    // Program phase cards
    gsap.from('.phase-card', {
        scrollTrigger: {
            trigger: '.program-section',
            start: "top 70%"
        },
        duration: 0.8,
        y: 100,
        opacity: 0,
        stagger: 0.15,
        ease: "power2.out"
    });

    // Therapy cards
    gsap.from('.therapy-card', {
        scrollTrigger: {
            trigger: '.therapy-section',
            start: "top 70%"
        },
        duration: 1,
        y: 80,
        opacity: 0,
        stagger: 0.2,
        ease: "power2.out"
    });

    // Parent tips
    gsap.from('.parent-tip', {
        scrollTrigger: {
            trigger: '.parents-section',
            start: "top 70%"
        },
        duration: 0.8,
        y: 60,
        opacity: 0,
        stagger: 0.15,
        ease: "power2.out"
    });

    // Timeline items
    gsap.from('.timeline-item', {
        scrollTrigger: {
            trigger: '.founder-section',
            start: "top 70%"
        },
        duration: 0.8,
        x: -50,
        opacity: 0,
        stagger: 0.2,
        ease: "power2.out"
    });
}

// Parallax Effects
function initParallax() {
    // Hero background parallax
    gsap.to('.hero-background', {
        scrollTrigger: {
            trigger: '.hero-section',
            start: "top top",
            end: "bottom top",
            scrub: true
        },
        y: -100,
        ease: "none"
    });

    // Lightning effect parallax
    gsap.to('.lightning-effect', {
        scrollTrigger: {
            trigger: '.hero-section',
            start: "top top",
            end: "bottom top",
            scrub: true
        },
        y: -50,
        ease: "none"
    });
}

// Modal Functions
function openModal() {
    modal.style.display = 'block';
    gsap.from('.modal-content', {
        duration: 0.5,
        scale: 0.7,
        opacity: 0,
        ease: "back.out(1.7)"
    });
}

function closeModalFunc() {
    gsap.to('.modal-content', {
        duration: 0.3,
        scale: 0.7,
        opacity: 0,
        ease: "power2.in",
        onComplete: () => {
            modal.style.display = 'none';
        }
    });
}

// Audio/Video Functions
function toggleAudio(e) {
    const button = e.currentTarget;
    const audioId = button.getAttribute('data-audio');
    const playIcon = button.querySelector('.play-icon');
    
    if (isPlaying[audioId]) {
        // Stop audio (placeholder)
        button.classList.remove('playing');
        playIcon.textContent = '▶';
        isPlaying[audioId] = false;
        
        // Show message for now
        showNotification('Audio stopped (Demo mode)');
    } else {
        // Play audio (placeholder)
        button.classList.add('playing');
        playIcon.textContent = '⏸';
        isPlaying[audioId] = true;
        
        // Show message for now
        showNotification('Playing audio (Demo mode)');
        
        // Auto stop after 3 seconds (demo)
        setTimeout(() => {
            if (isPlaying[audioId]) {
                button.classList.remove('playing');
                playIcon.textContent = '▶';
                isPlaying[audioId] = false;
            }
        }, 3000);
    }
}

// Trap Animation
function animateTrap(e) {
    const item = e.currentTarget;
    const trapType = item.getAttribute('data-trap');
    
    gsap.to(item, {
        duration: 0.3,
        scale: 0.95,
        ease: "power2.out",
        yoyo: true,
        repeat: 1
    });
    
    // Add destruction effect
    const destructionEffects = document.querySelectorAll('.effect-item');
    destructionEffects.forEach((effect, index) => {
        gsap.to(effect, {
            duration: 0.5,
            backgroundColor: 'rgba(255, 0, 64, 0.3)',
            delay: index * 0.1,
            yoyo: true,
            repeat: 1
        });
    });
    
    showNotification(`${trapType} addiction identified!`);
}

// Phase Slider
function changePhase(direction) {
    const totalPhases = phaseCards.length;
    
    // Remove active class from current phase
    phaseCards[currentPhase].classList.remove('active');
    
    // Calculate new phase
    currentPhase += direction;
    if (currentPhase >= totalPhases) currentPhase = 0;
    if (currentPhase < 0) currentPhase = totalPhases - 1;
    
    // Add active class to new phase
    phaseCards[currentPhase].classList.add('active');
    
    // Animate transition
    gsap.to('.program-slider', {
        duration: 0.5,
        x: -currentPhase * 320,
        ease: "power2.out"
    });
}

// Mirror Section
function nextQuestion() {
    const currentQ = questions[currentQuestion];
    const textarea = currentQ.querySelector('textarea');
    
    if (!textarea.value.trim()) {
        showNotification('Please answer the question first');
        return;
    }
    
    // Hide current question
    gsap.to(currentQ, {
        duration: 0.5,
        opacity: 0,
        y: -20,
        ease: "power2.in",
        onComplete: () => {
            currentQ.classList.remove('active');
            
            currentQuestion++;
            
            if (currentQuestion >= questions.length) {
                // Show final quote
                nextQuestionBtn.style.display = 'none';
                finalQuote.classList.remove('hidden');
                gsap.from(finalQuote, {
                    duration: 1,
                    scale: 0.8,
                    opacity: 0,
                    ease: "back.out(1.7)"
                });
            } else {
                // Show next question
                const nextQ = questions[currentQuestion];
                nextQ.classList.add('active');
                gsap.from(nextQ, {
                    duration: 0.5,
                    opacity: 0,
                    y: 20,
                    ease: "power2.out"
                });
            }
        }
    });
}

// Brotherhood Form
function handleBrotherhoodSubmit(e) {
    e.preventDefault();
    const formData = new FormData(e.target);
    const name = formData.get('name') || e.target.querySelector('input[type="text"]').value;
    const email = formData.get('email') || e.target.querySelector('input[type="email"]').value;
    
    if (name && email) {
        showNotification(`Welcome to the brotherhood, ${name}!`);
        e.target.reset();
        
        // Animate success
        gsap.to('.brotherhood-form', {
            duration: 0.5,
            scale: 1.05,
            ease: "power2.out",
            yoyo: true,
            repeat: 1
        });
    }
}

// Notification System
function showNotification(message) {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = 'notification';
    notification.textContent = message;
    notification.style.cssText = `
        position: fixed;
        top: 100px;
        right: 20px;
        background: rgba(0, 212, 255, 0.9);
        color: white;
        padding: 1rem 2rem;
        border-radius: 10px;
        z-index: 3000;
        font-weight: 600;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
    `;
    
    document.body.appendChild(notification);
    
    // Animate in
    gsap.from(notification, {
        duration: 0.5,
        x: 100,
        opacity: 0,
        ease: "power2.out"
    });
    
    // Remove after 3 seconds
    setTimeout(() => {
        gsap.to(notification, {
            duration: 0.5,
            x: 100,
            opacity: 0,
            ease: "power2.in",
            onComplete: () => {
                document.body.removeChild(notification);
            }
        });
    }, 3000);
}

// Keyboard shortcuts
document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape') {
        closeModalFunc();
    }
    if (e.key === 'ArrowLeft') {
        changePhase(-1);
    }
    if (e.key === 'ArrowRight') {
        changePhase(1);
    }
});

// Touch gesture support
let touchStartX = 0;
let touchStartY = 0;
let touchEndX = 0;
let touchEndY = 0;

function initTouchGestures() {
    // Phase slider touch gestures
    const programSlider = document.querySelector('.program-slider');
    if (programSlider) {
        programSlider.addEventListener('touchstart', handleTouchStart, {passive: true});
        programSlider.addEventListener('touchend', handleTouchEnd, {passive: true});
    }

    // Mirror section touch gestures
    const mirrorSection = document.querySelector('.mirror-section');
    if (mirrorSection) {
        mirrorSection.addEventListener('touchstart', handleTouchStart, {passive: true});
        mirrorSection.addEventListener('touchend', handleTouchEnd, {passive: true});
    }
}

function handleTouchStart(e) {
    touchStartX = e.changedTouches[0].screenX;
    touchStartY = e.changedTouches[0].screenY;
}

function handleTouchEnd(e) {
    touchEndX = e.changedTouches[0].screenX;
    touchEndY = e.changedTouches[0].screenY;
    handleSwipeGesture(e.currentTarget);
}

function handleSwipeGesture(element) {
    const deltaX = touchEndX - touchStartX;
    const deltaY = touchEndY - touchStartY;
    const minSwipeDistance = 50;

    // Only process horizontal swipes
    if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > minSwipeDistance) {
        if (element.classList.contains('program-slider')) {
            if (deltaX > 0) {
                changePhase(-1); // Swipe right = previous
            } else {
                changePhase(1);  // Swipe left = next
            }
        }
    }
}

// Enhanced mobile navigation
function enhanceMobileNav() {
    const navbar = document.querySelector('.navbar');
    let lastScrollY = window.scrollY;

    window.addEventListener('scroll', () => {
        const currentScrollY = window.scrollY;

        if (currentScrollY > lastScrollY && currentScrollY > 100) {
            // Scrolling down - hide navbar
            navbar.style.transform = 'translateY(-100%)';
        } else {
            // Scrolling up - show navbar
            navbar.style.transform = 'translateY(0)';
        }

        lastScrollY = currentScrollY;
    }, {passive: true});
}

// Intersection Observer for better performance
function initIntersectionObserver() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('in-view');

                // Trigger specific animations based on element
                if (entry.target.classList.contains('trap-item')) {
                    gsap.from(entry.target, {
                        duration: 0.6,
                        scale: 0.8,
                        opacity: 0,
                        ease: "back.out(1.7)"
                    });
                }

                if (entry.target.classList.contains('phase-card')) {
                    gsap.from(entry.target, {
                        duration: 0.8,
                        y: 50,
                        opacity: 0,
                        ease: "power2.out"
                    });
                }
            }
        });
    }, observerOptions);

    // Observe elements
    document.querySelectorAll('.trap-item, .phase-card, .therapy-card, .parent-tip').forEach(el => {
        observer.observe(el);
    });
}

// Device orientation handling
function handleOrientationChange() {
    window.addEventListener('orientationchange', () => {
        setTimeout(() => {
            // Recalculate dimensions after orientation change
            window.scrollTo(0, window.scrollY);

            // Refresh GSAP ScrollTrigger
            if (typeof ScrollTrigger !== 'undefined') {
                ScrollTrigger.refresh();
            }
        }, 100);
    });
}

// Performance optimizations
function initPerformanceOptimizations() {
    // Lazy load images when they come into view
    const images = document.querySelectorAll('img[data-src]');
    const imageObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.removeAttribute('data-src');
                imageObserver.unobserve(img);
            }
        });
    });

    images.forEach(img => imageObserver.observe(img));

    // Debounce scroll events
    let scrollTimeout;
    window.addEventListener('scroll', () => {
        if (scrollTimeout) {
            clearTimeout(scrollTimeout);
        }
        scrollTimeout = setTimeout(() => {
            // Scroll-dependent operations here
        }, 16); // ~60fps
    }, {passive: true});
}

// Accessibility enhancements
function initAccessibility() {
    // Keyboard navigation for custom elements
    document.querySelectorAll('.trap-item, .phase-card, .therapy-card').forEach(item => {
        item.setAttribute('tabindex', '0');
        item.setAttribute('role', 'button');

        item.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                item.click();
            }
        });
    });

    // Focus management for modals
    const modal = document.getElementById('pdfModal');
    const focusableElements = modal.querySelectorAll('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');
    const firstFocusable = focusableElements[0];
    const lastFocusable = focusableElements[focusableElements.length - 1];

    modal.addEventListener('keydown', (e) => {
        if (e.key === 'Tab') {
            if (e.shiftKey) {
                if (document.activeElement === firstFocusable) {
                    lastFocusable.focus();
                    e.preventDefault();
                }
            } else {
                if (document.activeElement === lastFocusable) {
                    firstFocusable.focus();
                    e.preventDefault();
                }
            }
        }
    });

    // Announce dynamic content changes
    const announcer = document.createElement('div');
    announcer.setAttribute('aria-live', 'polite');
    announcer.setAttribute('aria-atomic', 'true');
    announcer.style.cssText = 'position: absolute; left: -10000px; width: 1px; height: 1px; overflow: hidden;';
    document.body.appendChild(announcer);

    window.announce = function(message) {
        announcer.textContent = message;
    };
}

// Initialize all mobile and accessibility features
function initMobileFeatures() {
    initTouchGestures();
    enhanceMobileNav();
    initIntersectionObserver();
    handleOrientationChange();
    initPerformanceOptimizations();
    initAccessibility();
}

// Progress Tracking
function initProgressTracking() {
    window.addEventListener('scroll', updateProgress, {passive: true});
    updateProgress(); // Initial call
}

function updateProgress() {
    const scrollTop = window.pageYOffset;
    const docHeight = document.documentElement.scrollHeight - window.innerHeight;
    const scrollPercent = (scrollTop / docHeight) * 100;

    if (progressBar) {
        progressBar.style.width = scrollPercent + '%';
    }
}

// Section Indicators
function initSectionIndicators() {
    const observerOptions = {
        threshold: 0.3,
        rootMargin: '-20% 0px -20% 0px'
    };

    const sectionObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            const sectionId = entry.target.id;
            const indicator = document.querySelector(`[data-section="${sectionId}"]`);

            if (entry.isIntersecting) {
                // Remove active class from all indicators
                indicators.forEach(ind => ind.classList.remove('active'));
                // Add active class to current indicator
                if (indicator) {
                    indicator.classList.add('active');
                }

                // Update navigation active state
                document.querySelectorAll('.nav-menu a').forEach(link => {
                    link.classList.remove('active');
                    if (link.getAttribute('href') === `#${sectionId}`) {
                        link.classList.add('active');
                    }
                });
            }
        });
    }, observerOptions);

    // Observe all sections
    sections.forEach(section => {
        sectionObserver.observe(section);
    });
}

// Enhanced Section Visibility
function enhanceSectionVisibility() {
    // Add entrance animations for section content
    const sectionContent = document.querySelectorAll('.container');

    const contentObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('visible');

                // Animate section number
                const sectionNumber = entry.target.parentElement.querySelector('.section-number');
                if (sectionNumber) {
                    gsap.from(sectionNumber, {
                        duration: 1,
                        scale: 0,
                        rotation: 180,
                        opacity: 0,
                        ease: "back.out(1.7)",
                        delay: 0.3
                    });
                }
            }
        });
    }, {
        threshold: 0.2,
        rootMargin: '0px 0px -10% 0px'
    });

    sectionContent.forEach(content => {
        contentObserver.observe(content);
    });
}

// Keyboard Navigation
function initKeyboardNavigation() {
    let currentSectionIndex = 0;
    const sectionIds = ['hero', 'truth', 'program', 'therapy', 'mirror', 'parents', 'brotherhood', 'founder'];

    document.addEventListener('keydown', (e) => {
        if (e.ctrlKey || e.metaKey) return; // Ignore if modifier keys are pressed

        switch(e.key) {
            case 'ArrowDown':
            case 'PageDown':
                e.preventDefault();
                currentSectionIndex = Math.min(currentSectionIndex + 1, sectionIds.length - 1);
                scrollToSection(sectionIds[currentSectionIndex]);
                break;

            case 'ArrowUp':
            case 'PageUp':
                e.preventDefault();
                currentSectionIndex = Math.max(currentSectionIndex - 1, 0);
                scrollToSection(sectionIds[currentSectionIndex]);
                break;

            case 'Home':
                e.preventDefault();
                currentSectionIndex = 0;
                scrollToSection(sectionIds[0]);
                break;

            case 'End':
                e.preventDefault();
                currentSectionIndex = sectionIds.length - 1;
                scrollToSection(sectionIds[currentSectionIndex]);
                break;
        }
    });
}

function scrollToSection(sectionId) {
    const target = document.getElementById(sectionId);
    if (target) {
        gsap.to(window, {duration: 1.2, scrollTo: target, ease: "power2.inOut"});
    }
}

// Update the main initialization
document.addEventListener('DOMContentLoaded', function() {
    initAnimations();
    initEventListeners();
    initScrollAnimations();
    initParallax();
    initMobileFeatures();
    enhanceSectionVisibility();
    initKeyboardNavigation();
});

// Smooth scroll to top on page load
window.addEventListener('load', () => {
    window.scrollTo(0, 0);
});
