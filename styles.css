/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-bg: #0a0a0a;
    --secondary-bg: #1a1a1a;
    --accent-blue: #00d4ff;
    --accent-red: #ff0040;
    --text-primary: #ffffff;
    --text-secondary: #b0b0b0;
    --glass-bg: rgba(255, 255, 255, 0.1);
    --glass-border: rgba(255, 255, 255, 0.2);
    --shadow-glow: 0 0 30px rgba(0, 212, 255, 0.3);
    --shadow-red-glow: 0 0 30px rgba(255, 0, 64, 0.3);
}

body {
    font-family: 'Montserrat', sans-serif;
    background: var(--primary-bg);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Typography */
h1, h2, h3 {
    font-family: '<PERSON>', sans-serif;
    text-transform: uppercase;
    letter-spacing: 2px;
}

.section-title {
    font-size: 3rem;
    text-align: center;
    margin-bottom: 3rem;
    text-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
}

.text-blue { color: var(--accent-blue); }
.text-red { color: var(--accent-red); }
.highlight-red { color: var(--accent-red); text-shadow: var(--shadow-red-glow); }

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(10, 10, 10, 0.9);
    backdrop-filter: blur(10px);
    z-index: 1000;
    padding: 1rem 0;
    border-bottom: 1px solid var(--glass-border);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
}

.nav-logo {
    font-family: 'Anton', sans-serif;
    font-size: 1.5rem;
    color: var(--accent-blue);
    text-shadow: var(--shadow-glow);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-menu a {
    color: var(--text-primary);
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    position: relative;
}

.nav-menu a:hover {
    color: var(--accent-blue);
    text-shadow: var(--shadow-glow);
}

.nav-menu a::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--accent-blue);
    transition: width 0.3s ease;
}

.nav-menu a:hover::after {
    width: 100%;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: var(--text-primary);
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero-section {
    height: 100vh;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, #0a0a0a 0%, #1a1a1a 50%, #0a0a0a 100%);
}

.lightning-effect {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(circle at 20% 30%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 70%, rgba(255, 0, 64, 0.1) 0%, transparent 50%);
    animation: lightning 4s ease-in-out infinite alternate;
}

@keyframes lightning {
    0% { opacity: 0.3; }
    50% { opacity: 0.7; }
    100% { opacity: 0.3; }
}

.glitch-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: repeating-linear-gradient(
        90deg,
        transparent,
        transparent 2px,
        rgba(0, 212, 255, 0.03) 2px,
        rgba(0, 212, 255, 0.03) 4px
    );
    animation: glitch 2s linear infinite;
}

@keyframes glitch {
    0% { transform: translateX(0); }
    20% { transform: translateX(-2px); }
    40% { transform: translateX(2px); }
    60% { transform: translateX(-1px); }
    80% { transform: translateX(1px); }
    100% { transform: translateX(0); }
}

.hero-content {
    text-align: center;
    z-index: 2;
    max-width: 800px;
    padding: 0 20px;
}

.hero-title {
    font-size: 4rem;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.hero-highlight {
    color: var(--accent-blue);
    text-shadow: var(--shadow-glow);
    font-size: 5rem;
    display: block;
    margin: 0.5rem 0;
}

.glitch-text {
    position: relative;
    display: inline-block;
}

.glitch-text::before,
.glitch-text::after {
    content: attr(data-text);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.glitch-text::before {
    animation: glitch-1 0.5s infinite;
    color: var(--accent-red);
    z-index: -1;
}

.glitch-text::after {
    animation: glitch-2 0.5s infinite;
    color: var(--accent-blue);
    z-index: -2;
}

@keyframes glitch-1 {
    0%, 14%, 15%, 49%, 50%, 99%, 100% { transform: translate(0); }
    15%, 49% { transform: translate(-2px, -1px); }
}

@keyframes glitch-2 {
    0%, 20%, 21%, 62%, 63%, 99%, 100% { transform: translate(0); }
    21%, 62% { transform: translate(2px, 1px); }
}

.hero-subtitle {
    font-size: 1.5rem;
    margin-bottom: 3rem;
    color: var(--text-secondary);
    font-weight: 300;
}

.cta-button {
    position: relative;
    background: linear-gradient(45deg, var(--accent-blue), var(--accent-red));
    border: none;
    padding: 1rem 3rem;
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--text-primary);
    cursor: pointer;
    border-radius: 50px;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
    overflow: hidden;
}

.cta-button:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-glow);
}

.button-glow {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.cta-button:hover .button-glow {
    left: 100%;
}

.scroll-indicator {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 2;
}

.scroll-arrow {
    width: 30px;
    height: 30px;
    border-right: 2px solid var(--accent-blue);
    border-bottom: 2px solid var(--accent-blue);
    transform: rotate(45deg);
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: rotate(45deg) translateY(0); }
    40% { transform: rotate(45deg) translateY(-10px); }
    60% { transform: rotate(45deg) translateY(-5px); }
}

/* Truth Section */
.truth-section {
    padding: 100px 0;
    background: var(--secondary-bg);
    position: relative;
}

.truth-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    margin-bottom: 3rem;
}

.truth-left h3,
.truth-right h3 {
    font-size: 2rem;
    margin-bottom: 2rem;
    color: var(--accent-red);
    text-shadow: var(--shadow-red-glow);
}

.trap-icons {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
}

.trap-item {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.trap-item:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-red-glow);
}

.trap-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.destruction-animation {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: 20px;
    padding: 3rem;
    text-align: center;
}

.destruction-effects {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-top: 2rem;
}

.effect-item {
    background: rgba(255, 0, 64, 0.1);
    border: 1px solid var(--accent-red);
    border-radius: 10px;
    padding: 1rem;
    color: var(--accent-red);
    font-weight: 600;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 0.7; }
    50% { opacity: 1; }
    100% { opacity: 0.7; }
}

.truth-message {
    text-align: center;
    font-size: 1.5rem;
    font-weight: 600;
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: 15px;
    padding: 2rem;
}

/* Program Section */
.program-section {
    padding: 100px 0;
    background: var(--primary-bg);
}

.program-slider {
    display: flex;
    gap: 2rem;
    overflow-x: auto;
    padding: 2rem 0;
    scroll-behavior: smooth;
}

.phase-card {
    min-width: 300px;
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: 20px;
    padding: 3rem 2rem;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.phase-card.active {
    transform: scale(1.05);
    box-shadow: var(--shadow-glow);
    border-color: var(--accent-blue);
}

.phase-card:hover {
    transform: translateY(-10px);
}

.phase-number {
    font-family: 'Anton', sans-serif;
    font-size: 4rem;
    color: var(--accent-blue);
    text-shadow: var(--shadow-glow);
    margin-bottom: 1rem;
}

.phase-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.phase-card p {
    color: var(--text-secondary);
    margin-bottom: 2rem;
}

.download-btn {
    background: transparent;
    border: 2px solid var(--accent-blue);
    color: var(--accent-blue);
    padding: 0.8rem 2rem;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
}

.download-btn:hover {
    background: var(--accent-blue);
    color: var(--primary-bg);
    box-shadow: var(--shadow-glow);
}

.slider-controls {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 2rem;
}

.slider-btn {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    color: var(--text-primary);
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 1.5rem;
    transition: all 0.3s ease;
}

.slider-btn:hover {
    background: var(--accent-blue);
    box-shadow: var(--shadow-glow);
}

/* Glass Card Effect */
.glass-card {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: 20px;
    transition: all 0.3s ease;
}

.glass-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-glow);
}

/* Therapy Section */
.therapy-section {
    padding: 100px 0;
    background: var(--secondary-bg);
}

.therapy-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.therapy-card {
    padding: 3rem 2rem;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.play-button {
    width: 80px;
    height: 80px;
    background: linear-gradient(45deg, var(--accent-blue), var(--accent-red));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.play-button:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-glow);
}

.play-button.playing {
    animation: pulse-play 1s infinite;
}

@keyframes pulse-play {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.play-icon {
    color: white;
    font-size: 2rem;
    margin-left: 5px;
}

.therapy-card h3 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    color: var(--accent-blue);
}

.therapy-card p {
    color: var(--text-secondary);
    margin-bottom: 1rem;
}

.duration {
    color: var(--accent-red);
    font-weight: 600;
    font-size: 0.9rem;
}

/* Mirror Section */
.mirror-section {
    padding: 100px 0;
    background: var(--primary-bg);
    position: relative;
}

.mirror-content {
    max-width: 600px;
    margin: 0 auto;
    text-align: center;
}

.question-container {
    margin-bottom: 3rem;
}

.question {
    display: none;
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.5s ease;
}

.question.active {
    display: block;
    opacity: 1;
    transform: translateY(0);
}

.question h3 {
    font-size: 1.8rem;
    margin-bottom: 2rem;
    color: var(--accent-red);
    text-shadow: var(--shadow-red-glow);
}

.question textarea {
    width: 100%;
    min-height: 150px;
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: 15px;
    padding: 1.5rem;
    color: var(--text-primary);
    font-family: 'Montserrat', sans-serif;
    font-size: 1rem;
    resize: vertical;
}

.question textarea::placeholder {
    color: var(--text-secondary);
}

.question textarea:focus {
    outline: none;
    border-color: var(--accent-blue);
    box-shadow: var(--shadow-glow);
}

.mirror-btn {
    background: transparent;
    border: 2px solid var(--accent-red);
    color: var(--accent-red);
    padding: 1rem 3rem;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
}

.mirror-btn:hover {
    background: var(--accent-red);
    color: var(--primary-bg);
    box-shadow: var(--shadow-red-glow);
}

.final-quote {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: 20px;
    padding: 3rem;
    margin-top: 2rem;
}

.final-quote h3 {
    font-size: 2rem;
    margin-bottom: 2rem;
    color: var(--accent-blue);
    text-shadow: var(--shadow-glow);
}

.hidden {
    display: none;
}

/* Parents Section */
.parents-section {
    padding: 100px 0;
    background: var(--secondary-bg);
}

.parents-intro {
    text-align: center;
    margin-bottom: 4rem;
}

.parents-intro h3 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: var(--accent-blue);
}

.parents-intro p {
    font-size: 1.2rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

.parents-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.parent-tip {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: 20px;
    padding: 2.5rem 2rem;
    text-align: center;
    transition: all 0.3s ease;
}

.parent-tip:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-glow);
}

.tip-icon {
    font-size: 3rem;
    margin-bottom: 1.5rem;
}

.parent-tip h4 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.parent-tip p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Brotherhood Section */
.brotherhood-section {
    padding: 100px 0;
    background: var(--primary-bg);
}

.brotherhood-content {
    max-width: 500px;
    margin: 0 auto;
    text-align: center;
}

.brotherhood-quote h3 {
    font-size: 2rem;
    margin-bottom: 3rem;
    color: var(--accent-blue);
    text-shadow: var(--shadow-glow);
}

.brotherhood-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.brotherhood-form input {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: 15px;
    padding: 1.2rem 1.5rem;
    color: var(--text-primary);
    font-family: 'Montserrat', sans-serif;
    font-size: 1rem;
}

.brotherhood-form input::placeholder {
    color: var(--text-secondary);
}

.brotherhood-form input:focus {
    outline: none;
    border-color: var(--accent-blue);
    box-shadow: var(--shadow-glow);
}

/* Founder Section */
.founder-section {
    padding: 100px 0;
    background: var(--secondary-bg);
}

.founder-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 4rem;
    align-items: center;
}

.founder-image {
    position: relative;
}

.image-placeholder {
    width: 300px;
    height: 300px;
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: 50%;
    position: relative;
    overflow: hidden;
    margin: 0 auto;
}

.founder-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, var(--accent-blue) 0%, transparent 70%);
    opacity: 0.3;
    animation: glow-pulse 3s ease-in-out infinite;
}

@keyframes glow-pulse {
    0%, 100% { opacity: 0.3; transform: translate(-50%, -50%) scale(1); }
    50% { opacity: 0.6; transform: translate(-50%, -50%) scale(1.1); }
}

.timeline {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    margin-bottom: 3rem;
}

.timeline-item {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: 15px;
    padding: 1.5rem;
    position: relative;
    border-left: 4px solid var(--accent-blue);
}

.timeline-item h4 {
    color: var(--accent-blue);
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
}

.timeline-item p {
    color: var(--text-secondary);
}

.founder-quote {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
}

.founder-quote p {
    font-size: 1.3rem;
    font-style: italic;
    color: var(--accent-red);
    text-shadow: var(--shadow-red-glow);
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
}

.modal-content {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 20px;
    margin: 10% auto;
    padding: 3rem;
    width: 90%;
    max-width: 500px;
    text-align: center;
    position: relative;
}

.close {
    position: absolute;
    right: 20px;
    top: 20px;
    font-size: 2rem;
    cursor: pointer;
    color: var(--text-secondary);
    transition: color 0.3s ease;
}

.close:hover {
    color: var(--accent-red);
}

/* Touch and Mobile Enhancements */
.touch-friendly {
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
}

button, .cta-button, .download-btn, .mirror-btn, .play-button {
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
    min-height: 44px;
    min-width: 44px;
}

/* Swipe indicators for mobile */
.swipe-indicator {
    display: none;
    position: absolute;
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    color: var(--text-secondary);
    font-size: 0.8rem;
    opacity: 0.7;
}

/* Mobile-first responsive breakpoints */
@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }

    .hero-title {
        font-size: 2rem;
        line-height: 1.1;
    }

    .hero-highlight {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .cta-button {
        padding: 0.8rem 2rem;
        font-size: 1rem;
    }

    .section-title {
        font-size: 1.8rem;
        margin-bottom: 2rem;
    }

    .trap-item {
        padding: 1.5rem;
    }

    .trap-icon {
        font-size: 2rem;
    }

    .phase-card {
        padding: 2rem 1.5rem;
        min-width: 280px;
    }

    .phase-number {
        font-size: 3rem;
    }

    .therapy-card {
        padding: 2rem 1.5rem;
    }

    .play-button {
        width: 60px;
        height: 60px;
    }

    .play-icon {
        font-size: 1.5rem;
    }

    .question textarea {
        min-height: 120px;
        padding: 1rem;
    }

    .modal-content {
        margin: 15% auto;
        padding: 1.5rem;
        width: 95%;
    }

    .swipe-indicator {
        display: block;
    }
}

@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background: rgba(10, 10, 10, 0.95);
        width: 100%;
        text-align: center;
        transition: 0.3s;
        backdrop-filter: blur(10px);
        padding: 2rem 0;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-menu li {
        margin: 1rem 0;
    }

    .nav-menu a {
        font-size: 1.1rem;
        padding: 0.5rem 1rem;
        display: block;
        border-radius: 10px;
        transition: all 0.3s ease;
    }

    .nav-menu a:hover {
        background: var(--glass-bg);
    }

    .hamburger {
        display: flex;
    }

    .hamburger.active span:nth-child(1) {
        transform: rotate(-45deg) translate(-5px, 6px);
    }

    .hamburger.active span:nth-child(2) {
        opacity: 0;
    }

    .hamburger.active span:nth-child(3) {
        transform: rotate(45deg) translate(-5px, -6px);
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-highlight {
        font-size: 3rem;
    }

    .hero-subtitle {
        font-size: 1.3rem;
        margin-bottom: 2rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .truth-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .trap-icons {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .program-slider {
        overflow-x: auto;
        scroll-snap-type: x mandatory;
        -webkit-overflow-scrolling: touch;
        padding-bottom: 1rem;
    }

    .program-slider::-webkit-scrollbar {
        height: 4px;
    }

    .program-slider::-webkit-scrollbar-track {
        background: var(--glass-bg);
        border-radius: 2px;
    }

    .program-slider::-webkit-scrollbar-thumb {
        background: var(--accent-blue);
        border-radius: 2px;
    }

    .phase-card {
        min-width: 300px;
        scroll-snap-align: center;
        flex-shrink: 0;
    }

    .slider-controls {
        display: none;
    }

    .therapy-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .founder-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }

    .image-placeholder {
        width: 200px;
        height: 200px;
    }

    .parents-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .modal-content {
        margin: 20% auto;
        padding: 2rem;
    }

    .brotherhood-form {
        gap: 1rem;
    }

    .timeline {
        gap: 1.5rem;
    }

    .timeline-item {
        padding: 1rem;
    }
}

@media (max-width: 1024px) and (orientation: landscape) {
    .hero-section {
        height: 100vh;
        padding: 0 2rem;
    }

    .hero-title {
        font-size: 3rem;
    }

    .hero-highlight {
        font-size: 3.5rem;
    }

    .truth-content {
        grid-template-columns: 1fr 1fr;
        gap: 3rem;
    }
}

@media (min-width: 1200px) {
    .container {
        max-width: 1400px;
    }

    .hero-title {
        font-size: 5rem;
    }

    .hero-highlight {
        font-size: 6rem;
    }

    .section-title {
        font-size: 3.5rem;
    }

    .program-slider {
        gap: 3rem;
    }

    .phase-card {
        min-width: 350px;
    }

    .therapy-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 3rem;
    }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .hero-background {
        background-size: cover;
    }

    .lightning-effect {
        background-size: 200% 200%;
    }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .scroll-arrow {
        animation: none;
    }

    .lightning-effect {
        animation: none;
    }

    .glitch-overlay {
        animation: none;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    :root {
        --primary-bg: #000000;
        --secondary-bg: #111111;
    }
}

/* Print styles */
@media print {
    .navbar,
    .scroll-indicator,
    .modal,
    .play-button,
    .cta-button,
    .download-btn {
        display: none !important;
    }

    body {
        background: white !important;
        color: black !important;
    }

    .section-title {
        color: black !important;
        text-shadow: none !important;
    }
}
