// Audio Manager for Warrior Transformation Website
class AudioManager {
    constructor() {
        this.audioElements = {};
        this.currentlyPlaying = null;
        this.audioData = {
            audio1: {
                title: "<PERSON>mhe ab tak kis cheez ne rok rakha hai?",
                duration: "2:30",
                transcript: "Rise, warrior. Your old self must die today. The chains that bind you are not made of steel, but of habit. Break them now.",
                url: null // Will be replaced with actual audio file
            },
            audio2: {
                title: "Jagna seekho. Mard bano.",
                duration: "1:45", 
                transcript: "Awaken, learn to rise. Become the man you were meant to be. Your enemy is not outside - it's the weak version of you inside.",
                url: null
            },
            audio3: {
                title: "Rise, warrior. Your old self must die today.",
                duration: "2:15",
                transcript: "The path of transformation is not easy, but it is necessary. Every moment you delay is a moment stolen from your true potential.",
                url: null
            }
        };
        this.initializeAudio();
    }

    initializeAudio() {
        // Create audio elements for each therapy session
        Object.keys(this.audioData).forEach(audioId => {
            const audio = new Audio();
            audio.preload = 'metadata';
            
            // For demo purposes, we'll use text-to-speech or placeholder
            // In production, replace with actual audio files
            this.audioElements[audioId] = audio;
            
            // Add event listeners
            audio.addEventListener('loadedmetadata', () => {
                this.updateDuration(audioId, audio.duration);
            });
            
            audio.addEventListener('ended', () => {
                this.onAudioEnded(audioId);
            });
            
            audio.addEventListener('timeupdate', () => {
                this.updateProgress(audioId, audio.currentTime, audio.duration);
            });
        });
    }

    // Play or pause audio
    toggleAudio(audioId) {
        const audio = this.audioElements[audioId];
        const button = document.querySelector(`[data-audio="${audioId}"]`);
        const playIcon = button.querySelector('.play-icon');
        
        if (this.currentlyPlaying && this.currentlyPlaying !== audioId) {
            // Stop currently playing audio
            this.stopAudio(this.currentlyPlaying);
        }
        
        if (audio.paused) {
            // For demo, we'll simulate audio playback with motivational quotes
            this.playDemo(audioId, button, playIcon);
        } else {
            this.pauseAudio(audioId, button, playIcon);
        }
    }

    // Demo playback with text display
    playDemo(audioId, button, playIcon) {
        const audioInfo = this.audioData[audioId];
        button.classList.add('playing');
        playIcon.textContent = '⏸';
        this.currentlyPlaying = audioId;
        
        // Show transcript in a modal-like overlay
        this.showTranscript(audioInfo.title, audioInfo.transcript);
        
        // Simulate audio duration
        const duration = this.parseDuration(audioInfo.duration);
        this.simulatePlayback(audioId, duration);
        
        // Add pulsing animation
        gsap.to(button, {
            duration: 1,
            scale: 1.1,
            ease: "power2.inOut",
            yoyo: true,
            repeat: -1
        });
    }

    pauseAudio(audioId, button, playIcon) {
        button.classList.remove('playing');
        playIcon.textContent = '▶';
        this.currentlyPlaying = null;
        
        // Stop pulsing animation
        gsap.killTweensOf(button);
        gsap.to(button, {duration: 0.3, scale: 1});
        
        this.hideTranscript();
    }

    stopAudio(audioId) {
        const button = document.querySelector(`[data-audio="${audioId}"]`);
        const playIcon = button.querySelector('.play-icon');
        
        if (button) {
            this.pauseAudio(audioId, button, playIcon);
        }
    }

    onAudioEnded(audioId) {
        const button = document.querySelector(`[data-audio="${audioId}"]`);
        const playIcon = button.querySelector('.play-icon');
        
        button.classList.remove('playing');
        playIcon.textContent = '▶';
        this.currentlyPlaying = null;
        
        gsap.killTweensOf(button);
        gsap.to(button, {duration: 0.3, scale: 1});
        
        this.hideTranscript();
        this.showNotification('Audio session completed');
    }

    simulatePlayback(audioId, duration) {
        setTimeout(() => {
            if (this.currentlyPlaying === audioId) {
                this.onAudioEnded(audioId);
            }
        }, duration * 1000);
    }

    parseDuration(durationStr) {
        const parts = durationStr.split(':');
        return parseInt(parts[0]) * 60 + parseInt(parts[1]);
    }

    updateDuration(audioId, duration) {
        const durationElement = document.querySelector(`[data-audio="${audioId}"]`).parentElement.querySelector('.duration');
        if (durationElement) {
            const minutes = Math.floor(duration / 60);
            const seconds = Math.floor(duration % 60);
            durationElement.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
        }
    }

    updateProgress(audioId, currentTime, duration) {
        // Update progress bar if exists
        const progressBar = document.querySelector(`[data-audio="${audioId}"]`).parentElement.querySelector('.progress-bar');
        if (progressBar) {
            const progress = (currentTime / duration) * 100;
            progressBar.style.width = `${progress}%`;
        }
    }

    showTranscript(title, transcript) {
        // Create transcript overlay
        const overlay = document.createElement('div');
        overlay.className = 'transcript-overlay';
        overlay.innerHTML = `
            <div class="transcript-content">
                <button class="transcript-close">&times;</button>
                <h3>${title}</h3>
                <div class="transcript-text">
                    <p>${transcript}</p>
                </div>
                <div class="audio-visualizer">
                    <div class="visualizer-bar"></div>
                    <div class="visualizer-bar"></div>
                    <div class="visualizer-bar"></div>
                    <div class="visualizer-bar"></div>
                    <div class="visualizer-bar"></div>
                </div>
            </div>
        `;
        
        // Add styles
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            backdrop-filter: blur(10px);
            z-index: 2500;
            display: flex;
            align-items: center;
            justify-content: center;
        `;
        
        const content = overlay.querySelector('.transcript-content');
        content.style.cssText = `
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 3rem;
            max-width: 600px;
            width: 90%;
            text-align: center;
            position: relative;
        `;
        
        const closeBtn = overlay.querySelector('.transcript-close');
        closeBtn.style.cssText = `
            position: absolute;
            right: 20px;
            top: 20px;
            background: none;
            border: none;
            color: #ff0040;
            font-size: 2rem;
            cursor: pointer;
        `;
        
        const visualizer = overlay.querySelector('.audio-visualizer');
        visualizer.style.cssText = `
            display: flex;
            justify-content: center;
            gap: 5px;
            margin-top: 2rem;
        `;
        
        // Style visualizer bars
        const bars = overlay.querySelectorAll('.visualizer-bar');
        bars.forEach((bar, index) => {
            bar.style.cssText = `
                width: 4px;
                height: 20px;
                background: linear-gradient(45deg, #00d4ff, #ff0040);
                border-radius: 2px;
                animation: visualizer ${0.5 + index * 0.1}s ease-in-out infinite alternate;
            `;
        });
        
        // Add visualizer animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes visualizer {
                0% { height: 10px; }
                100% { height: 40px; }
            }
        `;
        document.head.appendChild(style);
        
        document.body.appendChild(overlay);
        
        // Animate in
        gsap.from(content, {
            duration: 0.5,
            scale: 0.8,
            opacity: 0,
            ease: "back.out(1.7)"
        });
        
        // Close functionality
        closeBtn.addEventListener('click', () => this.hideTranscript());
        overlay.addEventListener('click', (e) => {
            if (e.target === overlay) this.hideTranscript();
        });
    }

    hideTranscript() {
        const overlay = document.querySelector('.transcript-overlay');
        if (overlay) {
            gsap.to(overlay.querySelector('.transcript-content'), {
                duration: 0.3,
                scale: 0.8,
                opacity: 0,
                ease: "power2.in",
                onComplete: () => {
                    document.body.removeChild(overlay);
                }
            });
        }
    }

    showNotification(message) {
        const notification = document.createElement('div');
        notification.className = 'audio-notification';
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 100px;
            right: 20px;
            background: linear-gradient(45deg, #00d4ff, #ff0040);
            color: white;
            padding: 1rem 2rem;
            border-radius: 10px;
            z-index: 3000;
            font-weight: 600;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        `;
        
        document.body.appendChild(notification);
        
        gsap.from(notification, {
            duration: 0.5,
            x: 100,
            opacity: 0,
            ease: "power2.out"
        });
        
        setTimeout(() => {
            gsap.to(notification, {
                duration: 0.5,
                x: 100,
                opacity: 0,
                ease: "power2.in",
                onComplete: () => {
                    if (document.body.contains(notification)) {
                        document.body.removeChild(notification);
                    }
                }
            });
        }, 3000);
    }

    // Method to add real audio files when available
    loadAudioFile(audioId, audioUrl) {
        if (this.audioElements[audioId]) {
            this.audioElements[audioId].src = audioUrl;
            this.audioData[audioId].url = audioUrl;
        }
    }

    // Get all audio data for external use
    getAudioData() {
        return this.audioData;
    }
}

// Initialize audio manager when DOM is loaded
let audioManager;
document.addEventListener('DOMContentLoaded', () => {
    audioManager = new AudioManager();
    
    // Replace the existing toggleAudio function in script.js
    window.toggleAudio = function(e) {
        const button = e.currentTarget;
        const audioId = button.getAttribute('data-audio');
        audioManager.toggleAudio(audioId);
    };
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AudioManager;
}
